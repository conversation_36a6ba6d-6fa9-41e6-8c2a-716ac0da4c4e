/**
 * CxOne Client with SignalR WebSocket Support
 *
 * Usage Example:
 * var cxOne = getCxOneInstance();
 *
 * // Initialize SignalR connection
 * cxOne.initializeSignalRConnection("https://your-signalr-hub.com/callhub", {
 *     accessTokenFactory: function() { return "your-auth-token"; }
 * }).then(function(success) {
 *     if (success) {
 *         console.log("SignalR connected successfully");
 *     }
 * });
 *
 * // Check connection status
 * var status = cxOne.getSignalRStatus();
 * console.log("Connection status:", status);
 *
 * // Send message to hub
 * cxOne.sendSignalRMessage("RegisterAgent", { agentId: "agent123" });
 *
 * // Stop connection when done
 * cxOne.stopSignalRConnection();
 */

var _cxOneInstance = null;

/**
 * 
 * @return
 */
function getCxOneInstance() {
	if (_cxOneInstance === null) {
		_cxOneInstance = cxOneFactory();
	}
	
	return _cxOneInstance;
}

/**
 * 
 * @return
 */
function cxOneFactory() {

/*
            listenForNewCalls: false,
            viewCallHistory: false,
            clickToDialCxOne: false,
            clickToDialMsTeams: false
*/

	var state = {
        permissions: {
            CXONE_LISTEN_FOR_NEW_CALLS: false,
            CXONE_VIEW_CALL_HISTORY: false,
            CXONE_CLICK_TO_DIAL_CXONE: false,
            CXONE_CLICK_TO_DIAL_MS_TEAMS: false
        },
        currentCase: {
            callNo: 0
        },
		demoGraphics: null,
        callHistory: {
            data: null,
            divId: "cxone-call-history",
            panelId: "panel-cxone-call-history",
            panelIdCounterWrapper: "panel-cxone-call-history-available",
            panelIdCounter: "panel-cxone-call-history-available-count"
        },
        socket: {
            host: "",
            port: "",
            url: ""
        },
        signalR: {
            connection: null,
            hubUrl: "",
            isConnected: false,
            isConnecting: false,
            reconnectAttempts: 0,
            maxReconnectAttempts: 10,
            reconnectDelay: 1000, // Start with 1 second
            maxReconnectDelay: 30000, // Max 30 seconds
            connectionId: null,
            lastHeartbeat: null,
            heartbeatInterval: null,
            connectionStartTime: null,
            messagesReceived: []
            
        }
	};

    var api = cxOneApi();
	


    /**
     * We do this once user gets to "main" screen of CLEO "webui_docview", get a 
     * connection to socket server and register for messages.
     * @param {socketHost, socketPort, socketUrl } options 
     */
    function initInMainScreen(MyGlobalSession, userPermissions) {
        if(!MyGlobalSession) {
            console.log("cxOneFactory.initInMainScreen() no MyGlobalSession");
            return;
        }
        if(!userPermissions) {
            console.log("cxOneFactory.initInMainScreen() no userPermissions");
            return;
        }

        initPermissions(userPermissions);

        if (!state.permissions.CXONE_LISTEN_FOR_NEW_CALLS) {
            console.log("cxOneFactory.initInMainScreen() no permission to listen for new calls");
            return;
        }
    

        if(!MyGlobalSession.CXONE_SOCKET_URL) {
            console.log("cxOneFactory.initInMainScreen() no socket url");
            return;
        }

        state.socket.host = MyGlobalSession.CXONE_SOCKET_URL;
        state.socket.port = MyGlobalSession.CXONE_SOCKET_PORT;
        state.socket.url = MyGlobalSession.CXONE_SOCKET_PATH;

        api.getSocketLoginPayload().then(function(cleoTokens) {
            if (resp.success) {
                state.socketLoginPayload = cleoTokens.data;
                 // TODO connect to socket server.  How is token passed, here or after connection
                 // and send register agent message?
                initializeSignalRConnection(state.socket.host + state.socket.url, {
                    accessTokenFactory: function() { return "your-auth-token"; }
                }).then(function(success) {
                    if (success) {
                        console.log("SignalR connected successfully");

                        // TODO get agent id from somewhere...and tokens in cleoTokens?
                        // TODO agentId...once LtpaToken verified will return user name
                        sendSignalRMessage("RegisterAgent", { agentId: "agent123" });
                    }
                });
            }
        }); 
    }


    /**
     * 
     * @param {*} MyGlobalSession 
     * @param {*} userPermissions 
     */
    function initNewCall(MyGlobalSession, userPermissions) {
        initPermissions(userPermissions);
        initGenericCall();
    }


    /**
     * 
     * @param {*} MyGlobalSession 
     * @param {*} userPermissions 
     */
    function initExistingCall() {

        var userPermissions = CallControllerClient.userPermissions.Permissions[CallControllerClient.getUserConfigRole()];

        initPermissions(userPermissions);
        initGenericCall();

        if (state.permissions.CXONE_VIEW_CALL_HISTORY) {
            loadCallHistory(state.currentCase.callNo);
        }
    }

    /**
     * Stuff similar to initNewCall() and initExistingCall()
     */
    function initGenericCall() {
        state.currentCase.callNo = CallControllerClient.getFieldValue("CallNo");
        // Show click to dial buttons if we have permissions.
    }


    /**
     * 
     * @param {*} userPermissions 
     */
    function initPermissions(userPermissions) {

        if (!userPermissions) {
            return;
        }

        state.permissions.CXONE_LISTEN_FOR_NEW_CALLS = userPermissions["GLOBAL.CXONE_LISTEN_FOR_NEW_CALLS"] ? true : false;
        state.permissions.CXONE_VIEW_CALL_HISTORY = userPermissions["GLOBAL.CXONE_VIEW_CALL_HISTORY"] ? true : false;
        state.permissions.CXONE_CLICK_TO_DIAL_CXONE = userPermissions["GLOBAL.CXONE_CLICK_TO_DIAL_CXONE"] ? true : false;
        state.permissions.CXONE_CLICK_TO_DIAL_MS_TEAMS = userPermissions["GLOBAL.CXONE_CLICK_TO_DIAL_MS_TEAMS"] ? true : false;
    }

    /**
     * Initialize SignalR connection with robust error handling and auto-reconnect
     * @param {string} hubUrl - The SignalR hub URL
     * @param {Object} options - Connection options
     * @return {Promise<boolean>} - Success status
     */
    function initializeSignalRConnection(hubUrl, options) {
        if (!hubUrl) {
            console.error("SignalR: Hub URL is required");
            return Promise.resolve(false);
        }

        state.signalR.hubUrl = hubUrl;

        // Configure connection options with production-ready settings
        var connectionOptions = {
            transport: signalR.HttpTransportType.WebSockets,
            logLevel: signalR.LogLevel.Information,
            skipNegotiation: true
        };

        // Merge user options (ES5 compatible way)
        if (options) {
            for (var key in options) {
                if (options.hasOwnProperty(key)) {
                    connectionOptions[key] = options[key];
                }
            }
        }

        try {
            // Create new connection.  Ideally we'd pass a cookie.
            state.signalR.connection = new signalR.HubConnectionBuilder()
                .withUrl(hubUrl, connectionOptions)
                .withAutomaticReconnect({
                    nextRetryDelayInMilliseconds: function(retryContext) {
                        // Exponential backoff with jitter
                        var delay = Math.min(
                            state.signalR.reconnectDelay * Math.pow(2, retryContext.previousRetryCount),
                            state.signalR.maxReconnectDelay
                        );
                        // Add jitter (±25%)
                        var jitter = delay * 0.25 * (Math.random() - 0.5);
                        return Math.max(1000, delay + jitter);
                    }
                })
                .build();

            // Set up event handlers
            setupSignalREventHandlers();

            // Start the connection
            return startSignalRConnection();

        } catch (error) {
            console.error("SignalR: Failed to initialize connection", error);
            return Promise.resolve(false);
        }
    }

    /**
     * Set up SignalR event handlers for connection lifecycle and message processing
     */
    function setupSignalREventHandlers() {
        var connection = state.signalR.connection;

        // Connection started
        connection.onreconnecting(function(error) {
            console.warn("SignalR: Connection lost, attempting to reconnect...", error);
            state.signalR.isConnected = false;
            state.signalR.isConnecting = true;
            clearHeartbeat();
        });

        // Connection reconnected
        connection.onreconnected(function(connectionId) {
            console.log("SignalR: Reconnected successfully", connectionId);
            state.signalR.isConnected = true;
            state.signalR.isConnecting = false;
            state.signalR.reconnectAttempts = 0;
            state.signalR.connectionId = connectionId;
            state.signalR.connectionStartTime = new Date();
            startHeartbeat();
        });

        // Connection closed
        connection.onclose(function(error) {
            console.warn("SignalR: Connection closed", error);
            state.signalR.isConnected = false;
            state.signalR.isConnecting = false;
            state.signalR.connectionId = null;
            clearHeartbeat();

            // Attempt manual reconnection if not already reconnecting
            if (state.signalR.reconnectAttempts < state.signalR.maxReconnectAttempts) {
                setTimeout(function() {
                    attemptReconnection();
                }, calculateReconnectDelay());
            }
        });

        // Set up message handlers
        connection.on("NewCallNotification", function(payload) {
            try {
                console.log("SignalR: Received NewCallNotification", payload);
                processPayloadForNewCall(payload);
            } catch (error) {
                console.error("SignalR: Error processing NewCallNotification", error);
            }
        });

        // Generic message handler for other message types
        connection.on("SystemMessage", function(message) {
            console.log("SignalR: Received SystemMessage", message);
            // Handle system messages as needed
        });
    }

    /**
     * Start the SignalR connection
     * @return {Promise<boolean>} - Success status
     */
    function startSignalRConnection() {
        if (state.signalR.isConnecting || state.signalR.isConnected) {
            console.log("SignalR: Connection already active");
            return Promise.resolve(true);
        }

        state.signalR.isConnecting = true;

        // got to do this catch rubbish because of Notes v9 client.
        var prom = state.signalR.connection.start();
        prom
            .then(function() {
                console.log("SignalR: Connected successfully");
                state.signalR.isConnected = true;
                state.signalR.isConnecting = false;
                state.signalR.reconnectAttempts = 0;
                state.signalR.connectionId = state.signalR.connection.connectionId;
                state.signalR.connectionStartTime = new Date();
                startHeartbeat();
                return true;
            });

        prom["catch"](function(error) {
            console.error("SignalR: Failed to start connection", error);
            state.signalR.isConnecting = false;
        });

        return prom;
    }

    /**
     * Stop the SignalR connection gracefully
     * @return {Promise<void>}
     */
    function stopSignalRConnection() {
        clearHeartbeat();

        if (state.signalR.connection) {
            console.log("SignalR: Stopping connection...");
            state.signalR.isConnected = false;
            state.signalR.isConnecting = false;

            var prom = state.signalR.connection.stop();
            // return state.signalR.connection.stop()
            prom
                .then(function() {
                    console.log("SignalR: Connection stopped successfully");
                    state.signalR.connection = null;
                    state.signalR.connectionId = null;
                })

            prom["catch"](function(error) {
                console.error("SignalR: Error stopping connection", error);
            });

            return prom;
        }

        return Promise.resolve();
    }

    /**
     * Attempt manual reconnection with exponential backoff
     */
    function attemptReconnection() {
        if (state.signalR.isConnected || state.signalR.isConnecting) {
            return;
        }

        if (state.signalR.reconnectAttempts >= state.signalR.maxReconnectAttempts) {
            console.error("SignalR: Max reconnection attempts reached");
            return;
        }

        state.signalR.reconnectAttempts++;
        console.log("SignalR: Attempting reconnection #" + state.signalR.reconnectAttempts);

        startSignalRConnection()
            .then(function(success) {
                if (!success) {
                    setTimeout(function() {
                        attemptReconnection();
                    }, calculateReconnectDelay());
                }
            });
    }

    /**
     * Calculate reconnection delay with exponential backoff
     * @return {number} - Delay in milliseconds
     */
    function calculateReconnectDelay() {
        var delay = Math.min(
            state.signalR.reconnectDelay * Math.pow(2, state.signalR.reconnectAttempts),
            state.signalR.maxReconnectDelay
        );
        // Add jitter (±25%)
        var jitter = delay * 0.25 * (Math.random() - 0.5);
        return Math.max(1000, delay + jitter);
    }

    /**
     * Start heartbeat mechanism to monitor connection health
     */
    function startHeartbeat() {
        clearHeartbeat();

        state.signalR.heartbeatInterval = setInterval(function() {
            if (state.signalR.isConnected && state.signalR.connection) {
                // Send ping to server
                var prom = state.signalR.connection.invoke("Ping");
                // state.signalR.connection.invoke("Ping")
                prom
                    .then(function() {
                        state.signalR.lastHeartbeat = new Date();
                    })

                prom["catch"](function(error) {
                    console.warn("SignalR: Heartbeat failed", error);
                    // Connection might be lost, let the built-in reconnect handle it
                });
            }
        }, 30000); // Send heartbeat every 30 seconds
    }

    /**
     * Clear heartbeat interval
     */
    function clearHeartbeat() {
        if (state.signalR.heartbeatInterval) {
            clearInterval(state.signalR.heartbeatInterval);
            state.signalR.heartbeatInterval = null;
        }
    }

    /**
     * Get current SignalR connection status
     * @return {Object} - Connection status information
     */
    function getSignalRStatus() {
        return {
            isConnected: state.signalR.isConnected,
            isConnecting: state.signalR.isConnecting,
            connectionId: state.signalR.connectionId,
            reconnectAttempts: state.signalR.reconnectAttempts,
            connectionStartTime: state.signalR.connectionStartTime,
            lastHeartbeat: state.signalR.lastHeartbeat,
            hubUrl: state.signalR.hubUrl
        };
    }

    /**
     * Send a message to the SignalR hub
     * @param {string} methodName - Hub method name
     * @param {*} data - Data to send
     * @return {Promise<*>} - Promise resolving to the result
     */
    function sendSignalRMessage(methodName, data) {
        if (!state.signalR.isConnected || !state.signalR.connection) {
            console.warn("SignalR: Cannot send message - not connected");
            return Promise.reject(new Error("SignalR connection not available"));
        }

        var prom = state.signalR.connection.invoke(methodName, data);
        prom
            .then(function(result) {
                console.log("SignalR: Message sent successfully", methodName, result);
                return result;
            });

        prom["catch"](function(error) {
            console.error("SignalR: Failed to send message", methodName, error);
            throw error;
        });

        // return state.signalR.connection.invoke(methodName, data)
        //     .catch(function(error) {
        //         console.error("SignalR: Failed to send message", methodName, error);
        //         throw error;
        //     });
    }

    /*
     * 
     * @param payload {
     * 	ContactID: string;      Unique ID for the call session
     * 	CaseID?: string;        ID of the matched case, if available
     * 	UserEmail: string;      Agent’s email address - User Principal Name
     * 	Type: string;           One of NLP_MATCHED, NLP_UNMATCHED, NO_PDI, SMS_MATCHED 
     * 	PhoneNumber: string;    The phone number the patient is calling from
     * }
     * @return void
     */
    function processPayloadForNewCall(payload) {
        var serviceName = "CAS";
        var callServiceAlt = "";
        var cleoClientService = "";

        if(!state.permissions.LISTEN_FOR_NEW_CALLS) {
            // so this means a user is set up to listen for new calls, a specific message has been sent to them, but 
            // they don't have permission to listen for new calls.  This is some weird edge case
            // that I can't see happening, but...
            console.log("cxOneFactory.processPayloadForNewCall() no permission to listen for new calls");
            return;
        }


        // keep track of the messages received.
        state.signalR.messagesReceived.push(payload);

        if (payload && payload.CaseID && payload.CaseID.length > 0) {
            // We don't care how it was matched..
            // We can launch the case directly and load whatever info was entered.
            // var launchOptions = {
            //     cxOneCallNo: payload.CaseID // this triggers loadNewCall() when new case launched.
            // }
            // CallControllerClient.loadNewCallDocument(serviceName, callServiceAlt, cleoClientService, launchOptions);
            openCall(payload.CaseID);   
            return;
        }

        // if no case number, we have 2 choices:
        // NO_PDI: the demographic data passed, so launch a new case, I guess that save user having to create a new
        // case, or would that be confusing????...
        if (payload && payload.Type && payload.Type === "NO_PDI") {
            //  ...load a new case.
            CallControllerClient.loadNewCallDocument(serviceName, callServiceAlt, cleoClientService);
            return;
        }

        // Send user to a grid and tey can see if they can find case...or would it be just as simple to launch 
        // a new case and get user to go through standard: Surname, first name, DOB, etc.?????
        // loadSomeView()...?
        window.openViewPanel('CXONE', 60000, '')
    }

	
	/**
	 * on opening a new call, if it has been created by cxOne, we need to load the demographics.
	 * @param callNo
	 * @return
	 */
	function loadNewCall(callNo) {
		return api.getDemographics(callNo).then(function(resp) {
			if (resp.success) {
				state.demoGraphics = resp.data;
			}
			return resp;
		})
	}
	
	/**
	 * Once a new case is opened, we need to map the demographics to the case.
	 * @return
	 */
	function mapDemographicsToCase(userLookupController) {
		
		if (!state.demoGraphics) {
			console.log("cxOneFactory.mapDemographicsToCase() no demographics to load");
			return;
		}
		
		var FIELD_SUFFIX = userLookupController.getFIELD_SUFFIX();	
	
		// <FIELDS_SPECIFIC_TO_SEARCH>
		userLookupController.setFieldAndUpdateStore("firstName", "SearchForename", state.demoGraphics.CallForename)
		userLookupController.setFieldAndUpdateStore("lastname", "SearchSurname", state.demoGraphics.CallSurname)
		userLookupController.setFieldAndUpdateStore("gender", "SearchMF", state.demoGraphics.CallMF)
		userLookupController.setFieldAndUpdateStore("dob", "SearchDOB", state.demoGraphics.CallDOB)
		userLookupController.setFieldAndUpdateStore("postCode", "SearchPostCode", state.demoGraphics.CallPostCode)
		// </FIELDS_SPECIFIC_TO_SEARCH>
		

		$("#CallAddress1" + FIELD_SUFFIX).val(state.demoGraphics.CallAddress1)
		$("#CallAddress2" + FIELD_SUFFIX).val(state.demoGraphics.CallAddress2)
		$("#CallAddress3" + FIELD_SUFFIX).val(state.demoGraphics.CallAddress3)
		$("#CallTown" + FIELD_SUFFIX).val(state.demoGraphics.CallTown)
		$("#CallPostCode" + FIELD_SUFFIX).val(state.demoGraphics.CallPostCode)
		
		$("#PatientAddress1" + FIELD_SUFFIX).val(state.demoGraphics.PatientAddress1)
		$("#PatientAddress2" + FIELD_SUFFIX).val(state.demoGraphics.PatientAddress2)
		$("#PatientAddress3" + FIELD_SUFFIX).val(state.demoGraphics.PatientAddress3)
		$("#PatientTown" + FIELD_SUFFIX).val(state.demoGraphics.PatientTown)
		$("#PatientPostCode" + FIELD_SUFFIX).val(state.demoGraphics.PatientPostCode)
		
		$("#CallSymptoms" + FIELD_SUFFIX).val(state.demoGraphics.CallSymptoms)
		
		$("#CallCName").val(state.demoGraphics.CallCName)
		
		// should we add a value that doesn't exist in CLEO list?
		$("#CallCRel").val(state.demoGraphics.CallCRel)
		
		// should we add a value that doesn't exist in CLEO list?
		$("#CallEthnicity").val(state.demoGraphics.CallEthnicity)
		
		
		$("#CallTelNo_R").val(state.demoGraphics.CallTelNo_R)
		
		
		// <Service>
		var service = "CAS"
		$("#CallService").val("CAS");
		$("#CallServiceType").val("OOH");
		if ( CallControllerClient.isOneOneOneService(service) ){
			$("#CallServiceType").val("111");
		}
		$("#CallServiceType").html(service);
		
		CallControllerClient.setFieldValue("CallService_span", service);
		// </Service>
		
		CallControllerClient.setFieldValue("CallClassification", "Advice");
		CallControllerClient.setFieldValue("CallClassification_label", "Advice");
		
		ApplicationControllerClient.initProtocolSection();
		CallControllerClient.displayRecentCallsLabel();
		CallControllerClient.loadClassificationChoices(CallControllerClient.getFieldValue("CallClassification"));
		
		// if 1 to 1 match, has been NHS traced and verified... do we need to issue a search?...can we just proceed to "main" form?
		userLookupController.startSearch("TEMPLATE");
		
	}
	

    /**
     * Opening a call from a grid where the call is a partial inbound case received from the telephony app.
     * @param {*} callNumber 
     */
    function openCallFromGrid(callNumber) {
        var serviceName = "CAS";
        var callServiceAlt = "";
        var cleoClientService = "";

        var launchOptions = {
            cxOneCallNo: callNumber // this triggers loadNewCall() when new case launched.
            }
        CallControllerClient.loadNewCallDocument(serviceName, callServiceAlt, cleoClientService, launchOptions);
    }

    /**
     * 
     * @param {*} callId 
     */
    function loadCallHistory(callId) {

        if(!state.permissions.CXONE_VIEW_CALL_HISTORY) {
            return;
        }

        // show the panel if it's hidden.
        var panel = document.getElementById(state.callHistory.panelId);
        if (panel) {
            panel.style.display = '';
        }

        api.getCallsForCase(callId).then(function(resp) {
            if (resp.success) {
                state.callHistory.data = resp.data;
                displayCallHistory(state.callHistory);
            }
        });
    }

    /**
     * 
     * @param {*} callHistory 
     * @returns 
     */
    function displayCallHistory(callHistory) {
        var targetDiv = document.getElementById(callHistory.divId);
        if (!targetDiv) {
            console.error('Target div not found:', callHistory.divId);
            return;
        }

        // Clear existing content
        targetDiv.innerHTML = '';

        // Check if we have data
        if (!callHistory.data || !Array.isArray(callHistory.data) || callHistory.data.length === 0) {
            targetDiv.innerHTML = '<p>No call history available.</p>';
            return;
        }

        // show the panel count wrapper if it's hidden.
        var panelCounterWrapper = document.getElementById(callHistory.panelIdCounterWrapper);
        if (panelCounterWrapper) {
            panelCounterWrapper.style.display = '';
        }

        // set the count of calls in the panel header.
        var panelCounter = document.getElementById(callHistory.panelIdCounter);
        if (panelCounter) {
            panelCounter.textContent = callHistory.data.length.toString();

        }

        // Create table
        var table = document.createElement('table');
        table.style.width = '100%';
        table.style.borderCollapse = 'collapse';
        table.style.border = '1px solid #ddd';

        // Create header
        var thead = document.createElement('thead');
        var headerRow = document.createElement('tr');
        headerRow.style.backgroundColor = '#f2f2f2';

        var headers = ['Call Time', 'User Name', 'Link'];
        headers.forEach(function(headerText) {
            var th = document.createElement('th');
            th.textContent = headerText;
            th.style.border = '1px solid #ddd';
            th.style.padding = '8px';
            th.style.textAlign = 'left';
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Create body
        var tbody = document.createElement('tbody');

        callHistory.data.forEach(function(call) {
            var row = document.createElement('tr');
            row.style.borderBottom = '1px solid #ddd';

            // Call Time column
            var timeCell = document.createElement('td');
            timeCell.style.border = '1px solid #ddd';
            timeCell.style.padding = '8px';

            // Format the date if it's a valid ISO string
            var formattedTime = call.callTime;
            try {
                var date = new Date(call.callTime);
                if (!isNaN(date.getTime())) {
                    formattedTime = date.toLocaleString();
                }
            } catch (e) {
                // Keep original format if parsing fails
            }
            timeCell.textContent = formattedTime;
            row.appendChild(timeCell);

            // User Name column
            var userCell = document.createElement('td');
            userCell.style.border = '1px solid #ddd';
            userCell.style.padding = '8px';
            userCell.textContent = call.userName || '';
            row.appendChild(userCell);

            // Link column
            var linkCell = document.createElement('td');
            linkCell.style.border = '1px solid #ddd';
            linkCell.style.padding = '8px';

            if (call.link) {
                var link = document.createElement('a');
                link.href = call.link;
                link.textContent = 'Open Call';
                link.target = '_blank';
                link.style.color = '#007bff';
                link.style.textDecoration = 'underline';
                linkCell.appendChild(link);
            } else {
                linkCell.textContent = 'No link available';
            }

            row.appendChild(linkCell);
            tbody.appendChild(row);
        });

        table.appendChild(tbody);
        targetDiv.appendChild(table);
    }


    /**
     * 
     * @param {*} callId 
     */
    function clickToDialCxOne(callId) {

        if(!state.permissions.CXONE_CLICK_TO_DIAL_CXONE) {
            return;
        }

        api.clickToDialCxOne(callId).then(function(resp) {
            if (resp.success) {
                // Probabaly a fire and forget...
            }
        });
    }


    /**
     * 
     * @param {*} callId 
     * @returns 
     */
    function clickToDialMsTeams(callId) {
        if(!state.permissions.CXONE_CLICK_TO_DIAL_MS_TEAMS) {
            return;
        }

        api.clickToDialMsTeams(callId).then(function(resp) {
            //  Don't know how the MS Teams integration works...
            if (resp.success) {
                window.open(resp.data, '_blank');
            }
        });
    }

	/**
     * 
     * @return
     */
	return {
        initInMainScreen: initInMainScreen,
        initNewCall: initNewCall,
        initExistingCall: initExistingCall,
        processPayloadForNewCall: processPayloadForNewCall,
		loadNewCall: loadNewCall,
        openCallFromGrid: openCallFromGrid,
		mapDemographicsToCase: mapDemographicsToCase,
        clickToDialCxOne: clickToDialCxOne,
        clickToDialMsTeams: clickToDialMsTeams,

        // SignalR connection management
        initializeSignalRConnection: initializeSignalRConnection,
        stopSignalRConnection: stopSignalRConnection,
        getSignalRStatus: getSignalRStatus,
        sendSignalRMessage: sendSignalRMessage
	}
}


/**
 * 
 * @returns 
 */
function cxOneApi() {


    var baseUrl = MyGlobalSession.Global_DB_Paths.HOST_PATH + "/" + MyGlobalSession.Global_DB_Paths.PATH_CALL + "/(cxoneapi)?openagent";

    /**
     * The Telephony app needs to know who the user is..but WE HAVE NO F£$%ing Federated authentication.
     * so we have to come up with all these stupid hand cranked hacks. The Ltpa token is HTTP only, so we can't get it.
     * But...now we're getting it back up via an api!!!!!!  THere must be a security violation there. 
     * As of yet not sure if we get a connection to signalR, then pass a payload with the ltpa token.
     * @returns {
        "success": true,
        "data": {
            "ltpaToken": "AAECAzY4ODQ4OTUwNjg4NEY5RDBDTj1EaXNwIENvbnQzL089c3RhZ2luZ89Otnkj9MA7Uh+d1Pf3xXljhVq4",
            "ssoToken": "ewfewfwefwfwefwefewwfewf"
        },
        "message": ""
        }
     */
    function getSocketLoginPayload() {
        var url = baseUrl + "&ACTION=GET_SOCKET_LOGIN_PAYLOAD";
        return localCache.getUrlDataWithCache(url, false);
    }

    /**
	 * 
	 * @param callNo
	 * @return
	  {
		  "success": true,
		  "data": {
		    "CallNo": 250681398,
		    "CxOneMode": 1,
		    "CallStatusValue": -100,
		    "CallForename": "John",
		    "CallSurname": "CxOneDoeTwo",
		    "CallMF": "Male",
		    "CallAddress1": "10 Downing Street",
		    "CallAddress2": "",
		    "CallAddress3": "",
		    "CallTown": "London",
		    "CallPostCode": "SW1A 1AA",
		    "PatientAddress1": "10 Downing Street",
		    "PatientAddress2": "",
		    "PatientAddress3": "",
		    "PatientTown": "London",
		    "PatientPostCode": "SW1A 1AA",
		    "CallCName": "Jane Doe",
		    "CallCRel": "Parent",
		    "CallDOB": "15/06/1985",
		    "CallEthnicity": "White British",
		    "CallLanguage": "English",
		    "ElectronicRecordConsent": "Yes",
		    "PreferredContact": "Phone",
		    "CallSymptoms": "some illness"
		  },
		  "message": ""
		}
	 */
	function getDemographics(callNo) {
		var url = baseUrl + "&ACTION=GET_DEMOGRAPHICS&CALLNO=" + callNo;
		return localCache.getUrlDataWithCache(url, false);
	}

    /**
     * 
     * @param caseId 
     * @return 
     * {
        "success": true,
        "data": [
            {
            "callTime": "2025-07-23T09:00:00Z",
            "link": "https://some.serer.com/link/12345",
            "userName": "Joe Bloggs"
            },
            {
            "callTime": "2025-07-27T11:03:23Z",
            "link": "https://some.serer.com/link/1234444",
            "userName": "Simon Bloggs"
            }
        ],
        "message": ""
        }
     */
    function getCallsForCase(caseId) {
        var url = baseUrl + "&ACTION=GET_CALLS_HISTORY_FOR_CASE&CALLNO=" + caseId;
        return localCache.getUrlDataWithCache(url, false);
    }

    /**
     * 
     * @param {*} caseId 
     * @returns 
     */
    function clickToDialCxOne(caseId) {
        var url = baseUrl + "&ACTION=CLICK_TO_DIAL_CX_ONE&CALLNO=" + caseId;
        return localCache.getUrlDataWithCache(url, false);
    }

    /**
     * 
     * @param {*} caseId 
     * @returns 
     */
    function clickToDialMsTeams(caseId) {
        var url = baseUrl + "&ACTION=CLICK_TO_DIAL_MS_TEAMS&CALLNO=" + caseId;
        return localCache.getUrlDataWithCache(url, false);
    }


    /**
     * 
     */
    return {
        getSocketLoginPayload: getSocketLoginPayload,
        getDemographics: getDemographics,
        getCallsForCase: getCallsForCase,
        clickToDialCxOne: clickToDialCxOne,
        clickToDialMsTeams: clickToDialMsTeams
    }
}